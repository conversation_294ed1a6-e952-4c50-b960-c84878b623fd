// ProviderForm.tsx
import React from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import { FormikHelpers, useFormik } from 'formik';
import * as Yup from 'yup';
import GoogleMapComponent from './GoogleMap'; // Adjust the path if needed
import { ProviderFormData, paymentOptions, daysOptions, providerStatus, providerStatusOptions, defaultLocation } from '../../types/global';
import { Password } from 'primereact/password';
import { Dropdown } from 'primereact/dropdown';

interface ProviderFormProps {
  visible: boolean;
  onHide: () => void;
  onSubmit: (values: ProviderFormData, actions: FormikHelpers<ProviderFormData>) => void;
  initialValues: ProviderFormData;
  editMode: boolean;
}

const ProviderForm: React.FC<ProviderFormProps> = ({
  visible,
  onHide,
  onSubmit,
  initialValues,
  editMode
}) => {

  // File input change handler – validates file type and size before setting value.
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, field: string) => {
    const file = event.currentTarget.files?.[0] || null;
    if (file) {
      const isValidSize = file.size >= 20 * 1024 && file.size <= 2000 * 1024;
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(file.type);
      if (!isValidSize) {
        // Replace with your own error notification function if needed.
        alert('File size must be between 20KB and 2MB');
        return;
      }
      if (!isValidType) {
        alert('Only JPG, JPEG, PNG, or PDF files are allowed');
        return;
      }
    }
    formik.setFieldValue(field, file);
  };

  const validationSchema = Yup.object({
    email: Yup.string().email('Invalid email format').optional(),
    name: Yup.string().required('Name is required'),
    description: Yup.string().required('Description is required'),
    isApproved: Yup.string().oneOf(providerStatus),
    photoId: Yup.mixed().nullable(),
    cannabisLicense: Yup.mixed().nullable(),
    resellersPermit: Yup.mixed().nullable(),
    street: Yup.string().required('Street is required'),
    city: Yup.string().required('City is required'),
    state: Yup.string().required('State is required'),
    country: Yup.string().required('Country is required'),
    zipCode: Yup.string()
      .matches(/^\d+$/, 'ZIP code must contain only numbers')
      .min(4, 'Too short')
      .max(10, 'Too long')
      .required('ZIP code is required'),
    latitude: Yup.number().required('Latitude is required'),
    longitude: Yup.number().required('Longitude is required'),
    radius: Yup.number().required('Radius is required'),
    paymentOption: Yup.array()
      .of(Yup.string().oneOf(paymentOptions))
      .min(1, 'At least one payment option is required')
      .required(),
    startTime: Yup.string()
      .matches(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, 'Start time must be in HH:mm format')
      .required('Start time is required'),
    endTime: Yup.string()
      .matches(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, 'End time must be in HH:mm format')
      .required('End time is required'),
    availableDays: Yup.array()
      .of(Yup.string().oneOf(daysOptions))
      .min(1, 'At least one day availability is required')
      .required('At least one available day is required')
  });

  const formik = useFormik<ProviderFormData>({
    initialValues: {
      email: initialValues.email || '',
      password: initialValues.password || '',
      name: initialValues.name || '',
      isApproved: initialValues.isApproved || 'approved',
      description: initialValues.description || '',
      photoId: initialValues.photoId || null,
      cannabisLicense: initialValues.cannabisLicense || null,
      resellersPermit: initialValues.resellersPermit || null,
      street: initialValues.street || '',
      city: initialValues.city || '',
      state: initialValues.state || '',
      country: initialValues.country || '',
      zipCode: initialValues.zipCode || "",
      latitude: initialValues.latitude || defaultLocation.latitude,
      longitude: initialValues.longitude || defaultLocation.longitude,
      radius: initialValues.radius || defaultLocation.radius,
      paymentOption: initialValues.paymentOption || [],
      startTime: initialValues.startTime || '06:00',
      endTime: initialValues.endTime || '23:00',
      availableDays: initialValues.availableDays || []
    },
    validationSchema,
    onSubmit: (values, actions) => {
      onSubmit(values, actions);
    },
    enableReinitialize: true
  });

  return (
    <Dialog
      header={editMode ? 'Edit Provider' : 'Create Provider'}
      visible={visible}
      onHide={onHide}
      style={{ width: '50vw' }}
    >
      <form onSubmit={formik.handleSubmit} encType="multipart/form-data" className="p-fluid">
        {/* Email */}
        <div className="field">
          <label htmlFor="email">Email</label>
          <InputText
            id="email"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.email && formik.errors.email && (
            <small className="p-error">{formik.errors.email}</small>
          )}
        </div>

        {/* Password */}
        {!!initialValues._id
          ? "" :
          <div className="field">
            <label htmlFor="password">Password</label>
            <Password
              id="password"
              name="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              feedback={false}
              toggleMask
              className="w-full mt-2"
              inputClassName="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
            />
            {formik.touched.password && formik.errors.password && (
              <small className="p-error">{formik.errors.password}</small>
            )}
          </div>
        }

        {/* Name */}
        <div className="field">
          <label htmlFor="name">Name</label>
          <InputText
            id="name"
            name="name"
            value={formik.values.name}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.name && formik.errors.name && (
            <small className="p-error">{formik.errors.name}</small>
          )}
        </div>

        {/* Description */}
        <div className="field">
          <label htmlFor="description">Description</label>
          <InputText
            id="description"
            name="description"
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.description && formik.errors.description && (
            <small className="p-error">{formik.errors.description}</small>
          )}
        </div>

        {/* Provider Status */}
        <div className="field">
          <label htmlFor="isApproved">Provider Status</label>
          <Dropdown
            id="isApproved"
            name="isApproved"
            value={formik.values.isApproved}
            options={providerStatusOptions}
            onChange={(e) => formik.setFieldValue('isApproved', e.value)}
            onBlur={() => formik.setFieldTouched('isApproved', true)}
            placeholder="Select category status"
          />
          {formik.touched.isApproved && formik.errors.isApproved && (
            <small className="p-error">{formik.errors.isApproved}</small>
          )}
        </div>

        {/* File uploads */}
        <div className="field">
          <label htmlFor="photoId">Photo ID</label>
          <input
            type="file"
            id="photoId"
            name="photoId"
            accept="image/jpeg, image/jpg, image/png, application/pdf"
            onChange={(e) => handleFileChange(e, 'photoId')}
            className="p-inputtext p-component"
          />
          {formik.touched.photoId && formik.errors.photoId && (
            <small className="p-error">{formik.errors.photoId as string}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="cannabisLicense">Cannabis License</label>
          <input
            type="file"
            id="cannabisLicense"
            name="cannabisLicense"
            accept="image/jpeg, image/jpg, image/png, application/pdf"
            onChange={(e) => handleFileChange(e, 'cannabisLicense')}
            className="p-inputtext p-component"
          />
          {formik.touched.cannabisLicense && formik.errors.cannabisLicense && (
            <small className="p-error">{formik.errors.cannabisLicense as string}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="resellersPermit">Resellers Permit</label>
          <input
            type="file"
            id="resellersPermit"
            name="resellersPermit"
            accept="image/jpeg, image/jpg, image/png, application/pdf"
            onChange={(e) => handleFileChange(e, 'resellersPermit')}
            className="p-inputtext p-component"
          />
          {formik.touched.resellersPermit && formik.errors.resellersPermit && (
            <small className="p-error">{formik.errors.resellersPermit as string}</small>
          )}
        </div>

        {/* Address Fields */}
        <div className="field">
          <label htmlFor="street">Street</label>
          <InputText
            id="street"
            name="street"
            value={formik.values.street}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.street && formik.errors.street && (
            <small className="p-error">{formik.errors.street}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="city">City</label>
          <InputText
            id="city"
            name="city"
            value={formik.values.city}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.city && formik.errors.city && (
            <small className="p-error">{formik.errors.city}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="state">State</label>
          <InputText
            id="state"
            name="state"
            value={formik.values.state}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.state && formik.errors.state && (
            <small className="p-error">{formik.errors.state}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="country">Country</label>
          <InputText
            id="country"
            name="country"
            value={formik.values.country}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.country && formik.errors.country && (
            <small className="p-error">{formik.errors.country}</small>
          )}
        </div>

        <div>
          <label htmlFor="zipCode" className="block text-sm font-medium">Zip Code</label>
          <InputText
            id="zipCode"
            name="zipCode"
            placeholder="Enter ZIP code (e.g. 10001)"
            type="text"
            keyfilter="int"
            value={formik.values.zipCode}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            className="w-full mt-2 p-2 border rounded-md"
          />
          {formik.touched.zipCode && formik.errors.zipCode && <small className="text-red-500">{formik.errors.zipCode}</small>}
        </div>

        {/* Location and Radius */}
        <div className="field">
          <label>Location</label>
          <div className="p-d-flex p-ai-center p-jc-between">
            <span>
              Lat: {formik.values.latitude}, Lng: {formik.values.longitude}
            </span>
          </div>
        </div>

        <div className="field">
          <label htmlFor="radius">Radius</label>
          <InputNumber
            id="radius"
            name="radius"
            value={formik.values.radius}
            onValueChange={(e) => formik.setFieldValue('radius', e.value ?? 0)}
            onBlur={formik.handleBlur}
            useGrouping={false}
          />
          {formik.touched.radius && formik.errors.radius && (
            <small className="p-error">{formik.errors.radius as string}</small>
          )}
        </div>

        <div className="field">
          <GoogleMapComponent
            lat={formik.values.latitude || defaultLocation.latitude}
            lng={formik.values.longitude || defaultLocation.longitude}
            radius={formik.values.radius ?? defaultLocation.radius}
            editable={true}
            onLocationChange={(lat, lng) => {
              formik.setFieldValue('latitude', lat);
              formik.setFieldValue('longitude', lng);
            }}
          />
          {/* <div className="p-text-right p-mt-2">
            <Button label="Done" onClick={() => setLocationPopupVisible(false)} />
          </div> */}
          {formik.touched.latitude && formik.errors.latitude && (
            <small className="p-error">{formik.errors.latitude as string}</small>
          )}
          {formik.touched.longitude && formik.errors.longitude && (
            <small className="p-error">{formik.errors.longitude as string}</small>
          )}
        </div>


        {/* Payment Options */}
        <div className="field">
          <label>Payment Options</label>
          <div className="p-d-flex p-flex-wrap">
            {paymentOptions.map((option) => (
              <div key={option} className="p-mr-3 p-mb-2">
                <input
                  type="checkbox"
                  id={`paymentOption-${option}`}
                  name="paymentOption"
                  value={option}
                  checked={formik.values.paymentOption ? formik.values.paymentOption.includes(option) : false}
                  onChange={(e) => {
                    const { value, checked } = e.target;
                    let newOptions = formik.values.paymentOption ? [...formik.values.paymentOption] : [];
                    if (checked)
                      newOptions.push(value);
                    else
                      newOptions = newOptions.filter(opt => opt !== value);

                    formik.setFieldValue('paymentOption', newOptions);
                  }}
                />
                <label htmlFor={`paymentOption-${option}`} className="p-ml-1">
                  {option}
                </label>
              </div>
            ))}
          </div>
          {formik.touched.paymentOption && formik.errors.paymentOption && (
            <small className="p-error">{formik.errors.paymentOption as string}</small>
          )}
        </div>

        {/* Time Fields */}
        <div className="field">
          <label htmlFor="startTime">Start Time</label>
          <InputText
            id="startTime"
            name="startTime"
            type="time"
            value={formik.values.startTime}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.startTime && formik.errors.startTime && (
            <small className="p-error">{formik.errors.startTime}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="endTime">End Time</label>
          <InputText
            id="endTime"
            name="endTime"
            type="time"
            value={formik.values.endTime}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
          {formik.touched.endTime && formik.errors.endTime && (
            <small className="p-error">{formik.errors.endTime}</small>
          )}
        </div>

        {/* Available Days */}
        <div className="field">
          <label>Available Days</label>
          <div className="p-d-flex p-flex-wrap">
            {daysOptions.map((day) => (
              <div key={day} className="p-mr-3 p-mb-2">
                <input
                  type="checkbox"
                  id={`availableDays-${day}`}
                  name="availableDays"
                  value={day}
                  checked={formik.values.availableDays ? formik.values.availableDays.includes(day) : false}
                  onChange={(e) => {
                    const { value, checked } = e.target;
                    let newDays = formik.values.availableDays ? [...formik.values.availableDays] : [];
                    if (checked)
                      newDays.push(value);
                    else
                      newDays = newDays.filter(d => d !== value);

                    formik.setFieldValue('availableDays', newDays);
                  }}
                />
                <label htmlFor={`availableDays-${day}`} className="p-ml-1">
                  {day}
                </label>
              </div>
            ))}
          </div>
          {formik.touched.availableDays && formik.errors.availableDays && (
            <small className="p-error">{formik.errors.availableDays as string}</small>
          )}
        </div>

        {/* Submit Button */}
        <div className="field">
          <Button type="submit" label={editMode ? 'Update Provider' : 'Create Provider'} className="p-mt-2" />
        </div>
      </form>
    </Dialog>
  );
};

export default ProviderForm;
