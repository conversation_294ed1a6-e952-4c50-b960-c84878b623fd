import React from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { useFormik, FieldArray, FormikProvider, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { QuestionFormData, OptionFormData } from '../../types/global'; // adjust your types accordingly
import { InputNumber } from 'primereact/inputnumber';

interface QuestionFormProps {
  visible: boolean;
  onHide: () => void;
  onSubmit: (values: QuestionFormData, actions: FormikHelpers<QuestionFormData>) => void;
  initialValues: Partial<QuestionFormData>;
  editMode: boolean;
}

const validationSchema = Yup.object({
  value: Yup.string().required('Question is required').trim().max(1000),
  sequence: Yup.number().required('Sequence is required'),
  options: Yup.array()
    .of(
      Yup.object({
        value: Yup.string().required('Option is required').trim().max(1000),
      })
    )
    .min(2, 'At least two options are required')
    .required(),
});

const QuestionForm: React.FC<QuestionFormProps> = ({
  visible,
  onHide,
  onSubmit,
  initialValues,
  editMode,
}) => {
  const formik = useFormik<QuestionFormData>({
    initialValues: {
      value: initialValues.value || '',
      sequence: initialValues.sequence || 0,
      options: (initialValues.options as OptionFormData[]) || [],
    },
    validationSchema,
    onSubmit: (values, actions) => {
      onSubmit(values, actions);
    },
    enableReinitialize: true,
  });

  return (
    <Dialog
      header={editMode ? 'Edit Question' : 'Create Question'}
      visible={visible}
      onHide={onHide}
      style={{ width: '50vw' }}
    >
      <FormikProvider value={formik}>
        <form onSubmit={formik.handleSubmit} className="p-fluid">
          {/* Question Text */}
          <div className="field">
            <label htmlFor="value">Question</label>
            <InputText
              id="value"
              name="value"
              value={formik.values.value}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.value && formik.errors.value && (
              <small className="p-error">{formik.errors.value}</small>
            )}
          </div>

          {/* Sequence */}
          <div className="field">
            <label htmlFor="sequence">Sequence</label>
            <InputNumber
              id="sequence"
              name="sequence"
              value={formik.values.sequence}
              onValueChange={(e) => formik.setFieldValue("sequence", e.value)}
              onBlur={formik.handleBlur}
              useGrouping={false} // Optional: disables comma grouping like 1,000
            />
            {formik.touched.sequence && formik.errors.sequence && (
              <small className="p-error">{formik.errors.sequence}</small>
            )}
          </div>


          {/* Options */}
          <div className="field">
            <label>Options</label>
            <FieldArray
              name="options"
              render={(arrayHelpers) => (
                <div>
                  {formik.values.options && formik.values.options.length > 0 ? (
                    formik.values.options.map((option, index) => (
                      <div key={index} className="p-mb-2">
                        <div className="p-inputgroup">
                          <InputText
                            name={`options[${index}].value`}
                            value={option.value}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            placeholder="Option value"
                          />
                          {/* For existing options (having an _id), render update and delete buttons */}
                          {option._id && (
                            <>
                              <span className="p-inputgroup-addon">
                                <Button
                                  icon="pi pi-trash"
                                  className="p-button-text p-button-danger"
                                  type="button"
                                  tooltip="Delete Option"
                                  onClick={() => arrayHelpers.remove(index)}
                                />
                              </span>
                            </>
                          )}
                          {/* For new options, show a remove button */}
                          {!option._id && (
                            <span className="p-inputgroup-addon">
                              <Button
                                icon="pi pi-minus-circle"
                                className="p-button-text p-button-danger"
                                type="button"
                                tooltip="Remove Option"
                                onClick={() => arrayHelpers.remove(index)}
                              />
                            </span>
                          )}
                        </div>
                        {formik.touched.options &&
                          formik.touched.options[index] &&
                          formik.errors.options &&
                          (formik.errors.options as any)[index] &&
                          (formik.errors.options as any)[index].value && (
                            <small className="p-error">
                              {(formik.errors.options as any)[index].value}
                            </small>
                          )}
                      </div>
                    ))
                  ) : (
                    <div>No options. Please add some.</div>
                  )}
                  <div className="p-mt-2">
                    <Button
                      type="button"
                      label="Add Option"
                      icon="pi pi-plus-circle"
                      onClick={() => arrayHelpers.push({ value: '' })}
                    />
                  </div>
                </div>
              )}
            />
            {formik.touched.options && typeof formik.errors.options === 'string' && (
              <small className="p-error">{formik.errors.options}</small>
            )}
          </div>

          <div className="field">
            <Button
              type="submit"
              label={editMode ? 'Update Question' : 'Create Question'}
              className="p-mt-2"
            />
          </div>
        </form>
      </FormikProvider>
    </Dialog>
  );
};

export default QuestionForm;
