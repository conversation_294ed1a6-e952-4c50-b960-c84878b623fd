import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import usePagination from '../../hooks/usePagination';
import { getOrders, updateOrderStatus, getProfile } from '../../apis/provider';
import { catchAsync, showSuccess } from '../../utils/helper';
import { Order, defaultPaginationValues } from '../../types/global';

const Orders: React.FC = () => {
    const [orders, setOrders] = useState<Order[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const [providerId, setProviderId] = useState<string | null>(null);
    const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
    const [searchText, setSearchText] = useState('');

    const statusOptions = [
        { label: 'Pending', value: 'pending' },
        { label: 'Completed', value: 'completed' },
        { label: 'Cancelled by Provider', value: 'cancelled_by_provider' },
        { label: 'Cancelled by User', value: 'cancelled_by_user' }
    ];


    // Fetch provider ID on component mount
    useEffect(() => {
        const fetchProviderId = async () => {
            catchAsync(async () => {
                const response = await getProfile();
                if (response.status && response.data._id) {
                    setProviderId(response.data._id);
                }
            }, { showToast: false });
        };

        fetchProviderId();
    }, []);

    // Fetch orders when provider ID or params change
    useEffect(() => {
        if (providerId) {
            fetchOrders();
        }
    }, [params, providerId]);

    const fetchOrders = async () => {
        if (!providerId) return;

        setLoading(true);
        catchAsync(async () => {
            const response = await getOrders(providerId, {
                page: params.page,
                // limit: params.limit,
                // sort: params.sortField && params.sortOrder
                //     ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
                //     : undefined,
                // search: params.search,
            });
            setOrders(response.data.results);
            setTotalRecords(response.data.totalResults);
        }).finally(() => setLoading(false));
    };

    const handleSearch = () => {
        setSearch(searchText);
    };

    const handleStatusUpdate = (order: Order, newStatus: string) => {
        confirmDialog({
            message: `Are you sure you want to change the order status to "${newStatus}"?`,
            header: 'Confirm Status Update',
            icon: 'pi pi-exclamation-triangle',
            accept: async () => {
                catchAsync(async () => {
                    await updateOrderStatus(order.order_id, newStatus);
                    showSuccess('Order status updated successfully');
                    fetchOrders();
                });
            }
        });
    };

    // Template functions
    const statusBodyTemplate = (rowData: Order) => {
        const getSeverity = (status: string) => {
            switch (status) {
                case 'pending': return 'warning';
                case 'confirmed': return 'info';
                case 'preparing': return 'secondary';
                case 'ready': return 'success';
                case 'delivered': return 'success';
                case 'cancelled': return 'danger';
                case 'cancelled_by_user': return 'danger';
                default: return 'info';
            }
        };

        const getDisplayValue = (status: string) => {
            return status === 'cancelled_by_user' ? 'Cancelled by User' : status.charAt(0).toUpperCase() + status.slice(1);
        };

        return <Tag value={getDisplayValue(rowData.status)} severity={getSeverity(rowData.status)} />;
    };

    const actionBodyTemplate = (rowData: Order) => {
        return (
            <Dropdown
                value={rowData.status}
                options={statusOptions}
                onChange={(e) => handleStatusUpdate(rowData, e.value)}
                placeholder="Update Status"
                className="w-full md:w-14rem"
            />
        );
    };

    const priceBodyTemplate = (rowData: Order) => {
        return `$${rowData.price.toFixed(2)}`;
    };

    const shippingChargesBodyTemplate = (rowData: Order) => {
        return rowData.shipping_charges ? `$${rowData.shipping_charges.toFixed(2)}` : 'Free';
    };

    const variantBodyTemplate = (rowData: Order) => {
        return rowData.variant_flavour || 'Default';
    };

    const orderDateBodyTemplate = (rowData: Order) => {
        return new Date(rowData.order_date).toLocaleDateString();
    };

    // if (!providerId) {
    //     return (
    //         <div className="p-4">
    //             <div className="bg-white p-4 rounded-lg shadow text-center">
    //                 <h3>Loading provider information...</h3>
    //             </div>
    //         </div>
    //     );
    // }

    return (
        <div className="pt-3">
            <div className="flex justify-between items-center mb-4">
                <h2>My Orders</h2>
            </div>

            <div className=" rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                    <IconField iconPosition="left">
                        <InputIcon className="pi pi-search" />
                        <InputText
                            value={searchText}
                            onChange={(e) => setSearchText(e.target.value)}
                            placeholder="Search orders..."
                            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                        />
                    </IconField>

                </div>

                <DataTable
                    value={orders}
                    lazy
                    paginator
                    first={(params.page - 1) * params.limit}
                    rows={params.limit}
                    totalRecords={totalRecords}
                    onPage={onPage}
                    onSort={onSort}
                    sortField={params.sortField}
                    sortOrder={params.sortOrder}
                    loading={loading}
                    rowsPerPageOptions={[10, 20, 50]}
                    className="p-datatable-striped"
                    removableSort
                >
                    <Column field="order_id" header="Order ID" sortable />
                    <Column field="product_name" header="Product" sortable />
                    <Column header="Variant" body={variantBodyTemplate} />
                    <Column field="quantity" header="Quantity" sortable />
                    <Column header="Price" body={priceBodyTemplate} sortable />
                    <Column header="Status" body={statusBodyTemplate} sortable />
                    <Column header="Order Date" body={orderDateBodyTemplate} sortable />
                    <Column field="customer_email" header="Customer Email" sortable />
                    <Column header="Shipping" body={shippingChargesBodyTemplate} />
                    <Column header="Actions" body={actionBodyTemplate} />
                </DataTable>
            </div>

            <ConfirmDialog />
        </div>
    );
};

export default Orders;
