import { But<PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { InputNumber } from 'primereact/inputnumber';
import { FormikHelpers, useFormik } from "formik";
import * as Yup from "yup";
import { catchAsync, handelFormData, handelResponse, showError } from "../../utils/helper";
import { useEffect, useState } from "react";
import { getProfile, setProfile } from "../../apis/provider";
import { daysOptions, defaultLocation, paymentOptions, Provider } from "../../types/global";
import GoogleMapComponent from "../../components/admin/GoogleMap";
import { useAuth } from "../../utils/AuthContext";

const Profile = () => {
    const { isActiveProvider } = useAuth();

    const isProviderProfile = localStorage.getItem("isProviderProfile");
    const providerEmail = localStorage.getItem("email")
    const [loading, setLoading] = useState<boolean>(false);
    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, field: string) => {
        const file = event.currentTarget.files?.[0] || null;

        if (file) {
            const isValidSize = file.size >= 20 * 1024 && file.size <= 2000 * 1024;
            const isValidType = ["image/jpeg", "image/jpg", "image/png", "application/pdf"].includes(file.type);

            if (!isValidSize) {
                showError("File size must be between 20KB and 2MB");
                return;
            }
            if (!isValidType) {
                showError("Only JPG, JPEG, PNG, or PDF files are allowed");
                return;
            }
        }

        formik.setFieldValue(field, file);
    };

    const formik = useFormik<Provider>({
        initialValues: {
           email: providerEmail ?? undefined,
            name: "",
            description: "",
            photoId: null,
            cannabisLicense: null,
            resellersPermit: null,
            street: "",
            city: "",
            state: "",
            country: "",
            zipCode: "",
            latitude: defaultLocation.latitude,
            longitude: defaultLocation.longitude,
            radius: defaultLocation.radius,
            paymentOption: [],
            startTime: "06:00",
            endTime: "23:00",
            availableDays: [],
        },
        validationSchema: Yup.object({
            // email: Yup.string().email("Invalid email format").optional(),
            name: Yup.string().required("Name is required"),
            description: Yup.string().required("Description is required"),
            photoId: Yup.mixed().nullable(),
            cannabisLicense: Yup.mixed().nullable(),
            resellersPermit: Yup.mixed().nullable(),
            street: Yup.string().required("Street is required"),
            city: Yup.string().required("City is required"),
            state: Yup.string().required("State is required"),
            country: Yup.string().required("Country is required"),
            zipCode: Yup.string()
                .matches(/^\d+$/, 'ZIP code must contain only numbers')
                .min(4, 'Too short')
                .max(10, 'Too long')
                .required('ZIP code is required'),
            latitude: Yup.number().required("Latitude is required"),
            longitude: Yup.number().required("Longitude is required"),
            radius: Yup.string().required("Radius is required"),
            paymentOption: Yup.array().of(Yup.string().oneOf(paymentOptions)).min(1, "At least one payment option is required").required(),
            startTime: Yup.string().matches(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, "Start time must be in HH:mm format").required(),
            endTime: Yup.string().matches(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, "End time must be in HH:mm format").required(),
            availableDays: Yup.array().of(Yup.string().oneOf(daysOptions)).min(1, "At least one day avilability is required").required("At least one available day is required"),
        }),
        onSubmit: async (values, actions: FormikHelpers<Provider>) => {

            setLoading(true);
            const formData = handelFormData(values);
            // if (formData) {
            //     console.log(formik.values)
            //     showError("Profile submitted");
            // } else {
            //     showError("Profile not submitted");
            // }

            catchAsync(
                async () => {
                    const response = await setProfile(formData);
                    handelResponse(response, actions);
                    // showError("Profile submitted");
                }, { showToast: false }
            ).finally(() => setLoading(false));
        },
    });

    const fetchProfileData = async () => {
        if (!isProviderProfile) return;
        setLoading(true);
        catchAsync(async () => {
            const response = await getProfile();
            if (response.status) {
                // List of keys to remove
                const keysToRemove = ["_id", "isApproved", "deleted", "createdAt", "updatedAt", "__v"];
                keysToRemove.forEach(key => {
                    delete response.data[key];
                });
                formik.setValues({
                    ...response.data,
                });
            } else {
                throw new Error(response.message || "Failed to fetch profile data");
            }
        }).finally(() => setLoading(false));
    };

    useEffect(() => {
        if (isActiveProvider)
            fetchProfileData();
    }, [isProviderProfile, isActiveProvider]);

    return (
        <div className="flex justify-center items-center ">
            <div className="bg-gray-800 rounded-2xl p-5 w-full max-w-lg">
                <h2 className="text-center text-2xl font-bold text-[#CE93D8] mb-4">Profile</h2>
                <form onSubmit={formik.handleSubmit} encType="multipart/form-data" className="space-y-4">
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium">
                            Email
                        </label>
                        <InputText
                            id="email"
                            name="email"
                            value={providerEmail}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            disabled={true}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.email && formik.errors.email && <small className="text-red-500">{formik.errors.email}</small>}
                    </div>
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium">
                            Name
                        </label>
                        <InputText
                            id="name"
                            name="name"
                            value={formik.values.name}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.name && formik.errors.name && <small className="text-red-500">{formik.errors.name}</small>}
                    </div>

                    <div>
                        <label htmlFor="description" className="block text-sm font-medium">
                            Description
                        </label>
                        <InputText
                            id="description"
                            name="description"
                            value={formik.values.description}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.description && formik.errors.description && <small className="text-red-500">{formik.errors.description}</small>}
                    </div>

                    {/* File uploads */}
                    <div>
                        <label htmlFor="photoId" className="block text-sm font-medium">Photo ID</label>
                        <input
                            type="file"
                            id="photoId"
                            name="photoId"
                            accept="image/jpeg, image/jpg, image/png, application/pdf"
                            onChange={(e) => handleFileChange(e, "photoId")}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.photoId && formik.errors.photoId && <small className="text-red-500">{formik.errors.photoId}</small>}

                    </div>
                    <div>
                        <label htmlFor="cannabisLicense" className="block text-sm font-medium">Cannabis License</label>
                        <input
                            type="file"
                            id="cannabisLicense"
                            name="cannabisLicense"
                            accept="image/jpeg, image/jpg, image/png, application/pdf"
                            onChange={(e) => handleFileChange(e, "cannabisLicense")}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.cannabisLicense && formik.errors.cannabisLicense && <small className="text-red-500">{formik.errors.cannabisLicense}</small>}
                    </div>

                    <div>
                        <label htmlFor="resellersPermit" className="block text-sm font-medium">Resellers Permit</label>
                        <input
                            type="file"
                            id="resellersPermit"
                            name="resellersPermit"
                            accept="image/jpeg, image/jpg, image/png, application/pdf"
                            onChange={(e) => handleFileChange(e, "resellersPermit")}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.resellersPermit && formik.errors.resellersPermit && <small className="text-red-500">{formik.errors.resellersPermit}</small>}
                    </div>

                    {/* Other fields (Address, Payment options, etc.) */}
                    <div className="grid grid-cols-2 mx-0 justify-between">
                        <div>
                            <label htmlFor="street" className="block text-sm font-medium">Street</label>
                            <InputText
                                id="street"
                                name="street"
                                value={formik.values.street}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.street && formik.errors.street && <small className="text-red-500">{formik.errors.street}</small>}
                        </div>
                        <div>
                            <label htmlFor="city" className="block text-sm font-medium">City</label>
                            <InputText
                                id="city"
                                name="city"
                                value={formik.values.city}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.city && formik.errors.city && <small className="text-red-500">{formik.errors.city}</small>}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 mx-0 justify-between">
                        <div>
                            <label htmlFor="state" className="block text-sm font-medium">State</label>
                            <InputText
                                id="state"
                                name="state"
                                value={formik.values.state}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.state && formik.errors.state && <small className="text-red-500">{formik.errors.state}</small>}
                        </div>
                        <div>
                            <label htmlFor="country" className="block text-sm font-medium">Country</label>
                            <InputText
                                id="country"
                                name="country"
                                value={formik.values.country}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.country && formik.errors.country && <small className="text-red-500">{formik.errors.country}</small>}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 mx-0 justify-between">
                        <div>
                            <label htmlFor="zipCode" className="block text-sm font-medium">Zip Code</label>
                            <InputText
                                id="zipCode"
                                name="zipCode"
                                placeholder="Enter ZIP code (e.g. 10001)"
                                type="text"
                                keyfilter="int"
                                value={formik.values.zipCode}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.zipCode && formik.errors.zipCode && <small className="text-red-500">{formik.errors.zipCode}</small>}
                        </div>
                        <div>
                            <label htmlFor="radius" className="block text-sm font-medium">Radius</label>
                            <InputNumber
                                id="radius"
                                name="radius"
                                value={formik.values.radius}
                                onValueChange={(e) => formik.setFieldValue('radius', e.value ?? 0)}
                                onBlur={formik.handleBlur}
                                useGrouping={false}
                                className="w-full mt-2 rounded-md"
                            />
                            {formik.touched.radius && formik.errors.radius && (
                                <small className="p-error">{formik.errors.radius as string}</small>
                            )}
                        </div>
                    </div>

                    {/* Location and Radius */}
                    <div className="grid grid-cols-2 px-2">
                        <label className="block text-sm font-medium">Location</label>
                        <div className="flex justify-between w-full">
                            <p className="text-sm font-medium">
                                Lat: <span className="ms-3 text-[#CE93D8]">{formik.values.latitude}</span>
                            </p>
                            <p className="text-sm font-medium">
                                Lng: <span className="ms-3 text-[#CE93D8]">{formik.values.longitude}</span>
                            </p>
                        </div>
                        <div className="relative block w-full">
                            <GoogleMapComponent
                                radius={formik.values.radius || defaultLocation.radius}
                                lat={formik.values.latitude || defaultLocation.latitude}
                                lng={formik.values.longitude || defaultLocation.longitude}
                                editable={true}
                                onLocationChange={(lat, lng) => {
                                    formik.setFieldValue('latitude', lat);
                                    formik.setFieldValue('longitude', lng);
                                }}
                            />
                        </div>
                    </div>

                    {/* Payment Options */}
                    <div>
                        <label htmlFor="paymentOption" className="block text-sm font-medium">
                            Payment Options
                        </label>
                        <div className="flex flex-wrap mt-2">
                            {['Credit Card', 'UPI', 'Debit Card', 'Net Banking'].map((option) => (
                                <div key={option} className="mr-4 mb-2">
                                    <input
                                        className="mr-2"
                                        type="checkbox"
                                        id={`paymentOption-${option}`}
                                        name="paymentOption"
                                        value={option}
                                        checked={formik.values.paymentOption.includes(option)}
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            const isChecked = e.target.checked;
                                            const newOptions = isChecked
                                                ? [...formik.values.paymentOption, value]
                                                : formik.values.paymentOption.filter(opt => opt !== value);
                                            formik.setFieldValue('paymentOption', newOptions);
                                        }}
                                    />
                                    <label htmlFor={`paymentOption-${option}`} className="text-sm">{option}</label>
                                </div>
                            ))}
                        </div>
                        {formik.touched.paymentOption && formik.errors.paymentOption ? (
                            <small className="text-red-500">{formik.errors.paymentOption}</small>
                        ) : null}
                    </div>

                    {/* Time (Start Time & End Time) */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label htmlFor="startTime" className="block text-sm font-medium">
                                Start Time
                            </label>
                            <InputText
                                id="startTime"
                                name="startTime"
                                type="time"
                                value={formik.values.startTime}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.startTime && formik.errors.startTime ? (
                                <small className="text-red-500">{formik.errors.startTime}</small>
                            ) : null}
                        </div>

                        <div>
                            <label htmlFor="endTime" className="block text-sm font-medium">
                                End Time
                            </label>
                            <InputText
                                id="endTime"
                                name="endTime"
                                type="time"
                                value={formik.values.endTime}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.endTime && formik.errors.endTime ? (
                                <small className="text-red-500">{formik.errors.endTime}</small>
                            ) : null}
                        </div>
                    </div>

                    {/* Available Days */}
                    <div>
                        <label htmlFor="availableDays" className="block text-sm font-medium">
                            Available Days
                        </label>
                        <div className="flex flex-wrap mt-2">
                            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                                <div key={day} className="mr-4 mb-2">
                                    <input
                                        type="checkbox"
                                        id={`availableDays-${day}`}
                                        name="availableDays"
                                        value={day}
                                        checked={formik.values.availableDays.includes(day)}
                                        className="mr-2"
                                        onChange={(e) => {
                                            const day = e.target.value;
                                            const isChecked = e.target.checked;
                                            const newDays = isChecked
                                                ? [...formik.values.availableDays, day]
                                                : formik.values.availableDays.filter(d => d !== day);
                                            formik.setFieldValue('availableDays', newDays);
                                        }}
                                    />
                                    <label htmlFor={`availableDays-${day}`} className="text-sm">{day}</label>
                                </div>
                            ))}
                        </div>
                        {formik.touched.availableDays && formik.errors.availableDays ? (
                            <small className="text-red-500">{formik.errors.availableDays}</small>
                        ) : null}
                    </div>

                    <Button
                        type="submit"
                        label="Update Profile"
                        className="w-full "
                        disabled={loading}
                    />
                </form>
            </div>
        </div>
    );
};

export default Profile;
