export const defaultPaginationValues: LazyParams = {
  page: 1,
  limit: 10,
  sortField: 'createdAt',
  sortOrder: -1,
  search: ''
};

export interface LazyParams {
  page: number;
  limit: number;
  sortField?: string;
  sortOrder?: 1 | -1;
  search?: string;
}

export interface Response {
  message: string;
  code: number;
  data: Object;
  status: boolean;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sort?: string;
  search?: string;
}

export const units = [
  { label: 'Gram', value: 'Gram' },
  { label: 'Kilogram', value: 'Kilogram' },
  { label: 'Milliliter', value: 'Milliliter' },
  { label: 'Liter', value: 'Liter' },
  { label: 'Piece', value: 'Piece' },
];

export const yesNoOptions = [
  { label: 'Yes', value: true },
  { label: 'No', value: false },
];

export const activeOrInactiveOptions = [
  { label: 'Active', value: true },
  { label: 'Inactive', value: false },
]

export type Unit = 'Gram' | 'Kilogram' | 'Milliliter' | 'Liter' | 'Piece';

// ################################################ For providers ################################################
export interface Provider {
  provider?: string;
  _id?: string;
  name: string;
  email?: string;
  description: string;
  street: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  latitude: number;
  longitude: number;
  radius: number;
  paymentOption: string[];
  startTime: string;
  endTime: string;
  availableDays: string[];
  isApproved?: 'pending' | 'approved' | 'rejected';
  cannabisLicense: string | null | File;
  resellersPermit: string | null | File;
  photoId: string | null | File;
  user?: string;
  createdAt?: string;
  updatedAt?: string;
  password?: string;
};

export const statusOptions = [
  { label: 'Pending', value: 'pending' },
  { label: 'Approved', value: 'approved' },
  { label: 'Rejected', value: 'rejected' }
];

export const providerStatus = ['pending', 'approved', 'rejected'];

export const providerStatusOptions = [
  { label: 'Approved', value: 'approved' },
  { label: 'Pending', value: 'pending' },
  { label: 'Rejected', value: 'rejected' },
]
export const paymentOptions = ['Offline', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking'];

export const daysOptions = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export type ProviderFormData = Partial<Omit<Provider, 'createdAt' | 'updatedAt' | 'user'>>;

export const defaultLocation = { latitude: 40.398033, longitude: -3.710935, radius: 1000 };

// ################################################ For Categories ################################################

export interface SubCategoryFormData {
  _id?: string;
  name: string;
  status?: boolean;
  // createdAt?: string;
  // updatedAt?: string;
}

export interface CategoryFormData {
  category?: string;
  name: string;
  status: boolean;
  subCategories: SubCategoryFormData[];
  createdAt?: string;
  updatedAt?: string;
}

export interface Category {
  _id: string;
  name: string;
  status?: boolean;
  createdAt?: string;
  updatedAt?: string;
  subCategories: SubCategoryFormData[];
}

// ################################################ For Questions ################################################
export interface OptionFormData {
  _id?: string; // present if the option already exists
  value: string;
  status?: boolean; // Optional: if you want to track active/inactive status
  // createdAt?: string;
  // updatedAt?: string;
}

export interface Question {
  _id: string;
  value: string;
  sequence: number;
  options: OptionFormData[];
  createdAt?: string;
  updatedAt?: string;
  // Add any other fields as needed
}

export interface QuestionFormData {
  question?: string; // Used for updates, holds the question id
  value: string;
  sequence: number;
  createdAt?: string;
  updatedAt?: string;
  options: OptionFormData[];
}

// ################################################ For Customers ################################################
export interface Customer {
  _id: string;
  email: string;
  username: string;
  photo?: string;
  bio?: string;
  emailPermission: boolean;
  notificationPermission: boolean;
  latitude?: number;
  longitude?: number;
  address: {
    phone?: string,
    pincode?: string,
    locality?: string,
    address_line?: string,
    city?: string,
    state?: string,
    landmark?: string,
  },
  user: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerFormData {
  _id?: string;
  customer?: string;
  email: string;
  password?: string;
  username: string;
  photo?: File | string;
  bio?: string;
  emailPermission: boolean;
  notificationPermission: boolean;
  latitude?: number;
  longitude?: number;
  address: {
    phone?: string,
    pincode?: string,
    locality?: string,
    address_line?: string,
    city?: string,
    state?: string,
    landmark?: string,
  },
}

export interface CustomerPayload extends Omit<CustomerFormData, 'latitude' | 'longitude'> {
  location: {
    type: 'Point';
    coordinates: [number, number];
  };
}

// ################################################ For Products ################################################
export interface VariantFormData {
  _id?: string;
  amount: number;
  unit: Unit;
  default: boolean;
  fake_price: number;
  price: number;
  flavour?: string;
  stock: number;
}

export interface ProductUsageFormData {
  _id?: string;
  question: string;
  option: string;
}

export interface ProductFormData {
  _id?: string;
  provider?: string;
  product?: string;
  name: string;
  description: string;
  category: string;
  subCategory: string;
  delivery: boolean;
  minDeliveryAmount: number;
  shippingCharges: number;
  photo: string[];
  variants: VariantFormData[];
  productUsages: ProductUsageFormData[];
  providerDetails: providerDetails[]
}

export interface Variant {
  _id: string;
  amount: number;
  unit: Unit;
  default: boolean;
  fake_price: number;
  price: number;
  flavour?: string;
  stock: number;
}

export interface ProductUsage {
  _id: string;
  question: string;
  option: string;
}

export interface providerDetails {
  name?: string;
  _id?: string;
}
export interface Product {
  _id: string;
  provider?: string;
  name: string;
  description: string;
  category: Category;
  subCategory: SubCategoryFormData;
  delivery: boolean;
  minDeliveryAmount: number;
  shippingCharges: number;
  photo: string[];
  variants: Variant[];
  productUsages: ProductUsage[];
  createdAt: string;
  updatedAt: string;
  categoryName?: string;
  subCategoryName?: string;
  providerDetails: providerDetails[]
}

export const mapProductToFormData = (product: Product): ProductFormData => ({
  provider: product.provider,
  name: product.name,
  description: product.description,
  category: product.category?._id || '',       // or any unique identifier property
  subCategory: product.subCategory?._id || '',   // similarly here
  delivery: product.delivery,
  minDeliveryAmount: product.minDeliveryAmount,
  shippingCharges: product.shippingCharges,
  photo: product.photo,
  variants: product.variants,
  productUsages: product.productUsages,
  providerDetails: product?.providerDetails

});

// ################################################ For Orders ################################################
export interface Order {
  order_id: string;
  product_name: string;
  variant_flavour: string;
  quantity: number;
  price: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled' | 'cancelled_by_user';
  order_date: string;
  customer_email: string;
  shipping_charges?: number;
  provider_id?: string;
  provider_name?: string;
}

// ################################################ For Reviews ################################################
export interface Review {
  _id: string;
  rating: number;
  title: string;
  experience: string;
  product_id: string;
  product_name: string;
  reviewed_on: string;
  customer_name: string;
  customer_id: string;
  order_id: string;
  provider_id: string;
}

// ################################################ For Deshboard count ################################################
export interface DashboardCountResponse {
  status: boolean;
  code: number;
  data: {
    totalCustomers: number;
    totalProviders: number;
    totalProducts: number;
    totalOrders: number;
  };
  message: string;
}

// ################################################ For Provider Deshboard count ################################################
export interface ProviderDashboardCountResponse {
  status: boolean;
  code: number;
  data: {
    totalReviews: number;
    totalProducts: number;
    totalOrders: number;
  };
  message: string;
}