import React, { useState, useEffect, useRef } from 'react';
import {
  GoogleMap,
  useLoadScript,
  MarkerF,
  Autocomplete,
} from '@react-google-maps/api';
import { CustomerFormData, defaultLocation } from '../../types/global';
import { InputText } from 'primereact/inputtext';

interface GoogleMapComponentProps {
  lat?: number;
  lng?: number;
  editable?: boolean;
  radius?: number;
  onLocationChange?: (lat: number, lng: number) => void;
    onAddressSelect?: (address: CustomerFormData['address']) => void;
}

const mapContainerStyle = {
  width: '100%',
  height: '400px',
};

const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({
  lat = defaultLocation.latitude,
  lng = defaultLocation.longitude,
  editable = false,
  radius,
  onLocationChange,
  onAddressSelect,
}) => {

  const [markerPosition, setMarkerPosition] = useState({ lat, lng });
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [circle, setCircle] = useState<google.maps.Circle | null>(null);

  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const apiKey = import.meta.env.VITE_APP_GOOGLE_MAPS_API_KEY!;
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: apiKey,
    libraries: ['places'],
  });

  // Emit location changes
  const emitLocation = (lat: number, lng: number) => {
    setMarkerPosition({ lat, lng });
    if (onLocationChange) onLocationChange(lat, lng);
  };

  // Marker drag
  const handleMarkerDragEnd = (e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      emitLocation(e.latLng.lat(), e.latLng.lng());
    }
  };

  // Map click
  const handleMapClick = (e: google.maps.MapMouseEvent) => {
    if (editable && e.latLng) {
      emitLocation(e.latLng.lat(), e.latLng.lng());
    }
  };

  // Autocomplete place select
const handlePlaceChanged = () => {
    const place = autocompleteRef.current?.getPlace();
    if (!place?.geometry?.location || !place?.address_components) return;

    const get = (type:string) =>
      place.address_components?.find(c => c.types.includes(type))?.long_name || '';

    const newLat = place.geometry.location.lat();
    const newLng = place.geometry.location.lng();

    // Construct the address object
    const addressData = {
      pincode: get('postal_code'),
      locality: get('sublocality_level_1'),
      address_line: place.formatted_address,
      city: get('locality'),
      state: get('administrative_area_level_1'),
      landmark: place.name,
    };

    setMarkerPosition({
      lat: newLat,
      lng: newLng,
    })
    // Send address info back to parent
    onAddressSelect?.(addressData);

    // Optional: emit lat/lng separately for marker update
    emitLocation?.(newLat, newLng);

    // Optional: move and zoom map
    if (mapInstance) {
      mapInstance.panTo({ lat: newLat, lng: newLng });
      mapInstance.setZoom(15);
    }
  };

  // Draw/update circle overlay
  useEffect(() => {
    if (!mapInstance) return;

    circle?.setMap(null);

    const newCircle = new google.maps.Circle({
      strokeColor: '#FF0000',
      strokeOpacity: 0.8,
      strokeWeight: 2,
      fillColor: '#FF0000',
      fillOpacity: 0.35,
      map: mapInstance,
      center: markerPosition,
      radius,
    });

    setCircle(newCircle);
    return () => newCircle.setMap(null);
  }, [markerPosition, radius, mapInstance]);

  // Sync prop updates
  useEffect(() => {
    emitLocation(lat, lng);
  }, [lat, lng]);

  if (loadError) {
    return (
      <div className="p-4 border border-red-200 rounded bg-red-50">
        <h4>Error loading Google Maps</h4>
        <p>{loadError.message}</p>
      </div>
    );
  }
  if (!isLoaded) {
    return (
      <div className="p-4 text-center border rounded">
        Loading Google Maps…
      </div>
    );
  }

  return (
    <div>
      <Autocomplete
        onLoad={(auto) => (autocompleteRef.current = auto)}
        onPlaceChanged={handlePlaceChanged}
      >
        <InputText
          ref={inputRef}
          placeholder="Search for a location…"
          style={{ width: '100%', marginBottom: 10 }}
        />
      </Autocomplete>

      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={markerPosition}
        zoom={12}
        onLoad={setMapInstance}
        onClick={handleMapClick}
      >
        <MarkerF
          position={markerPosition}
          draggable={editable}
          onDragEnd={handleMarkerDragEnd}
        />
      </GoogleMap>
    </div>
  );
};


export default GoogleMapComponent;