import  { useEffect, useState } from "react";
import { Package, ShoppingCart, Star } from "lucide-react";
import { ProviderDashboardCountResponse } from "../../types/global";
import {  getProviderDeshboardCount } from "../../apis/admin";

const Dashboard = () => {
        const [dashboardData, setDashboardData] = useState<ProviderDashboardCountResponse["data"] | null>(null);

    useEffect(() => {
        const fetchDashboardCounts = async () => {
            try {
                const res = await getProviderDeshboardCount();
                if (res.status) {
                    setDashboardData(res.data);
                }
            } catch (error) {
                console.error("Failed to fetch dashboard data", error);
            }
        };

        fetchDashboardCounts();
    }, []);
    const stats = [
        {
            title: "Total Products",
            value: dashboardData?.totalProducts,
            change: "+12%",
            changeType: "positive",
            icon: Package,
            color: "blue",
            bgColor: "bg-blue-500/10",
            iconColor: "text-blue-400",
            description: "Active products in inventory"
        },
        {
            title: "Total Orders",
            value: dashboardData?.totalOrders,
            change: "+8%",
            changeType: "positive",
            icon: ShoppingCart,
            color: "green",
            bgColor: "bg-green-500/10",
            iconColor: "text-green-400",
            description: "Orders this month"
        },
        {
            title: "Customer Reviews",
            value: dashboardData?.totalReviews,
            change: "+15%",
            changeType: "positive",
            icon: Star,
            color: "yellow",
            bgColor: "bg-yellow-500/10",
            iconColor: "text-yellow-400",
            description: "Total customer feedback"
        }
    ];

    return (
        <div className="min-h-screen  from-gray-900 to-gray-900">
            {/* Header Section */}
            <div className=" border-b border-gray-700/50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h1 className="text-2xl sm:text-3xl font-bold text-white">
                                Dashboard Overview
                            </h1>

                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-11 sm:px-6 lg:px-8 py-8">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    {stats.map((stat, index) => {
                        const Icon = stat.icon;
                        return (
                            <div
                                key={index}
                                className="group relative bg-gray-800/80 backdrop-blur-sm border border-gray-600/60 rounded-2xl p-5 hover:bg-gray-700/90 hover:border-gray-500/80 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl hover:shadow-black/30"
                            >
                                {/* Background Glow Effect */}
                                <div className={`absolute inset-0 ${stat.bgColor} rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>
                                
                                {/* Content */}
                                <div className="relative">
                                    <div className="flex items-start justify-between mb-4">
                                        <div className={`${stat.bgColor} ${stat.iconColor} p-2 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                                            <Icon className="w-10 h-10" />
                                        </div>
                                    </div>
                                    
                                    <div className="space-y-2">
                                        <p className="text-3xl sm:text-4xl font-bold text-white group-hover:text-white transition-colors">
                                            {stat.value||0}
                                        </p>
                                        <p className="text-base font-semibold text-gray-200">
                                            {stat.title||"--"}
                                        </p>
                                        <p className="text-sm text-gray-400">
                                            {stat.description||"--"}
                                        </p>
                                    </div>
                                </div>

                                {/* Hover indicator */}
                                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-b-2xl"></div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default Dashboard;