// import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import 'primeflex/primeflex.css';
import 'primeicons/primeicons.css';
import "primereact/resources/primereact.min.css";  
import "primereact/resources/themes/mdc-dark-deeppurple/theme.css";

import App from './App.tsx'
import { AuthProvider } from './utils/AuthContext.tsx'

createRoot(document.getElementById('root')!).render(
  // <StrictMode>
    <AuthProvider>
      <App />
    </AuthProvider>
  // </StrictMode>,
)
