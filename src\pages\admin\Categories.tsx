import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { <PERSON><PERSON> } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import usePagination from '../../hooks/usePagination';
import { getCategories, createCategory, updateCategory, deleteCategory, updateCategoryStatus } from '../../apis/admin';
import { catchAsync, handelResponse } from '../../utils/helper';
import CategoryForm from '../../components/admin/CategoryForm';
import { Category, CategoryFormData } from '../../types/global'; // Adjust types accordingly
import { defaultPaginationValues } from '../../types/global';
import { FormikHelpers } from 'formik';

const Categories: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [loading, setLoading] = useState(false);
  const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
  const [searchText, setSearchText] = useState('');
  const [visibleDialog, setVisibleDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  // formData holds the initial data for the CategoryForm. When editing,
  // it includes the category and its subcategories.
  const [formData, setFormData] = useState<Partial<CategoryFormData>>({});

  useEffect(() => {
    fetchCategories();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  const fetchCategories = async () => {
    setLoading(true);
    catchAsync(async () => {
      const response = await getCategories({
        page: params.page,
        limit: params.limit,
        sort:
          params.sortField && params.sortOrder
            ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
            : undefined,
        search: params.search,
      });
      setCategories(response.data.results);
      setTotalRecords(response.data.totalResults);
    }).finally(() => setLoading(false));
  };

  const handleCreate = () => {
    setSelectedCategory(null);
    setFormData({});
    setEditMode(false);
    setVisibleDialog(true);
  };

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setFormData(category);
    setEditMode(true);
    setVisibleDialog(true);
  };

  const handleDelete = (categoryId: string) => {
    confirmDialog({
      message: 'Are you sure you want to delete this category?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        catchAsync(async () => {
          const response = await deleteCategory(categoryId);
          handelResponse(response);
          if (response.status) fetchCategories();
        }, { showToast: true });
      },
    });
  };

  const handleStatusChange = (category: Category) => {
    catchAsync(async () => {
      const response = await updateCategoryStatus(category._id);
      handelResponse(response);
      if (response.status) fetchCategories();
    }, { showToast: true });
  };

  const handleSubmit = async (values: CategoryFormData, actions: FormikHelpers<CategoryFormData>) => {
    catchAsync(async () => {
      let response;
      if (editMode) {
        // For update, attach the category id to the values
        values.category = selectedCategory?._id;
        response = await updateCategory(values);
      } else {
        response = await createCategory(values);
      }
      handelResponse(response, actions);
      if(response.status){
        setVisibleDialog(false);
        fetchCategories();
      }
    }, { showToast: true });
  };

  // Render subcategories (if any) as comma separated list for the table
  const subCategoriesBodyTemplate = (rowData: Category) => {
    if (Array.isArray(rowData.subCategories)) {
      return rowData.subCategories.map((sc) => sc.name).join(', ');
    }
    return '';
  };

  const statusBodyTemplate = (rowData: Category) => {
    return (
      <span className={`p-tag ${rowData.status ? 'p-tag-success' : 'p-tag-danger'}`}>
        {rowData.status ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const actionBodyTemplate = (rowData: Category) => {
    return (
      <div className="flex gap-2">
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-success w-2 h-4"
          onClick={() => handleEdit(rowData)}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-danger w-2 h-4"
          onClick={() => handleDelete(rowData._id)}
        />
        <Button
          label={rowData.status ? 'Deactivate' : 'Activate'}
          className="p-button-rounded"
          style={{ minWidth: '90px', whiteSpace: 'nowrap' }}
          onClick={() => handleStatusChange(rowData)}
        />
      </div>
    );
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearch(searchText);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchText]);

  return (
    <div className="card p-fluid">
      <ConfirmDialog />

      <div className="flex justify-content-between align-items-center mb-4">
        <h2>Categories Management</h2>
        <div className="flex gap-2">
          <span className="p-input-icon-left">
            <IconField iconPosition="left">
              <InputIcon className="pi pi-search" />
              <InputText
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="Search categories..."
              />
            </IconField>
          </span>
          <Button label="Create Category" icon="pi pi-plus" onClick={handleCreate} />
        </div>
      </div>

      <DataTable
        value={categories}
        lazy
        paginator
        first={(params.page - 1) * params.limit}
        rows={params.limit}
        totalRecords={totalRecords}
        onPage={onPage}
        onSort={onSort}
        sortField={params.sortField}
        sortOrder={params.sortOrder}
        loading={loading}
        rowsPerPageOptions={[10, 20, 50]}
        className="p-datatable-striped"
        removableSort
      >
        <Column field="name" header="Name" sortable />
        <Column field="subCategories" header="Subcategories" body={subCategoriesBodyTemplate} />
        <Column field="status" header="Status" body={statusBodyTemplate} sortable />
        <Column header="Actions" body={actionBodyTemplate} />
      </DataTable>

      <CategoryForm
        visible={visibleDialog}
        onHide={() => setVisibleDialog(false)}
        onSubmit={handleSubmit}
        initialValues={formData}
        editMode={editMode}
      />
    </div>
  );
};

export default Categories;
