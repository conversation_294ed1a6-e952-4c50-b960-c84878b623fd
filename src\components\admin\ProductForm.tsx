// src/components/admin/ProductForm.tsx
import React, { useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import { useFormik, FieldArray, FormikProvider, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { Dropdown } from 'primereact/dropdown';
import { Category, ProductFormData, Question, units, yesNoOptions } from '../../types/global'; // Adjust types accordingly
// import { useAuth } from '../../utils/AuthContext';

interface ProductFormProps {
  isLoading: boolean;
  visible: boolean;
  onHide: () => void;
  onSubmit: (values: ProductFormData, actions: FormikHelpers<ProductFormData>) => void;
  initialValues: Partial<ProductFormData>;
  editMode: boolean;
  categories: Category[] | undefined;
  questions: Question[] | undefined;
}

// Modify the photo field validation to handle File objects (for new uploads)
// and URL strings (for existing photos). Also require at least one photo for create.
const validationSchema = Yup.object({
  provider: Yup.string().optional(),
  name: Yup.string().required('Product name is required').max(255),
  description: Yup.string().required('Description is required').max(1000),
  category: Yup.string().required('Category is required'),
  subCategory: Yup.string().required('Subcategory is required'),
  delivery: Yup.boolean().required('Delivery option is required'),
  minDeliveryAmount: Yup.number()
    .typeError('Minimum delivery amount must be a number')
    .required('Minimum delivery amount is required'),
  photo: Yup.array()
    .of(
      Yup.mixed().test('fileType', 'Unsupported file format', (value) => {
        if (value instanceof File) {
          return ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'].includes(
            value.type
          );
        }
        // If it's a string (existing URL), assume it's valid
        return true;
      })
    )
    .test('photoRequired', 'At least one photo is required', function (value) {
      // In update mode, the presence of a product ID (from initialValues) means files are optional.
      const { product } = this.parent;
      if (product) {
        return true;
      }
      return value && value.length > 0;
    }),
  variants: Yup.array()
    .of(
      Yup.object({
        amount: Yup.number()
          .typeError('Amount must be a number')
          .required('Amount is required'),
        unit: Yup.string()
          .oneOf(units.map((u) => u.value))
          .required('Unit is required'),
        default: Yup.boolean().required('Default selection is required'),
        fake_price: Yup.number()
          .typeError('Fake price must be a number')
          .required('Fake price is required'),
        price: Yup.number()
          .typeError('Price must be a number')
          .required('Price is required'),
        flavour: Yup.string().optional(),
        stock: Yup.number()
          .typeError('Stock must be a number')
          .required('Stock is required'),
      })
    )
    .min(1, 'At least one variant is required'),
  productUsages: Yup.array()
    .of(
      Yup.object({
        question: Yup.string().required('Question is required'),
        option: Yup.string().required('Option is required'),
      })
    )
    .test(
      'atLeastOne',
      'At least one product usage is required',
      (value) => (value || []).filter(v => v.option).length > 0
    ),
});


const ProductForm: React.FC<ProductFormProps> = ({
  visible,
  isLoading,
  onHide,
  onSubmit,
  initialValues,
  editMode,
  categories,
  questions,
}) => {
  // const { role } = useAuth();
  // Ensure categories and questions are always arrays
  const safeCategories = Array.isArray(categories) ? categories : [];
  const safeQuestions = Array.isArray(questions) ? questions : [];
  const formik = useFormik<ProductFormData>({
    initialValues: {
      provider: (initialValues?.providerDetails || [])[0]?._id || '',
      name: initialValues.name || '',
      description: initialValues.description || '',
      category: initialValues.category || '',
      subCategory: initialValues.subCategory || '',
      delivery: typeof initialValues.delivery === 'boolean' ? initialValues.delivery : true,
      minDeliveryAmount: initialValues.minDeliveryAmount || 0,
      shippingCharges: initialValues.shippingCharges || 0,
      photo: initialValues.photo || [],
      // product: initialValues?._id,
      variants: initialValues.variants || [
        {
          amount: 0,
          unit: 'Piece',
          default: false,
          fake_price: 0,
          price: 0,
          flavour: '',
          stock: 0,
        },
      ],
      productUsages: initialValues.productUsages || [
        {
          question: '',
          option: '',
        },
      ],

      // Include product ID (if any) so that our validation can detect edit mode.

    } as ProductFormData,
    validationSchema,
    onSubmit: (values, actions) => {
      onSubmit(values, actions);
    },
    enableReinitialize: true,
  });

  const getSubCategories = () => {
    const category = safeCategories.find((c) => c._id === formik.values.category);
    return category ? category.subCategories : [];
  };

  // Handler for file input changes.
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.currentTarget.files) {
      const files = Array.from(e.currentTarget.files);
      formik.setFieldValue('photo', files);
    }
  };


  useEffect(() => {
    if (Object.keys(formik.errors).length > 0) {
      console.log("Formik errors:", formik.errors);
    }
  }, [formik.errors]);

  useEffect(() => {
    if (safeQuestions.length > 0) {
      // Preserve existing valid usages and add new questions
      const existingUsages = formik.values.productUsages.filter(u => u.option !== '');
      const newUsages = safeQuestions
        .filter(q => !existingUsages.some(u => u.question === q._id))
        .map(q => ({ question: q._id, option: '' }));

      formik.setFieldValue('productUsages', [...existingUsages, ...newUsages]);
    }
  }, [safeQuestions]);
  useEffect(() => {
    if (editMode && safeCategories.length > 0) {
      // Set category if initial value exists
      if (initialValues.category) {
        formik.setFieldValue('category', initialValues.category);

        // Set subcategory if it exists in the category's subcategories
        const category = safeCategories.find(c => c._id === initialValues.category);
        if (category && initialValues.subCategory) {
          formik.setFieldValue('subCategory', initialValues.subCategory);
        }
      }
    }
  }, [safeCategories, editMode, initialValues.category, initialValues.subCategory]);

  return (
    <Dialog
      header={editMode ? 'Edit Product' : 'Create Product'}
      visible={visible}
      onHide={onHide}
      style={{ width: '70vw' }}
    >
      <FormikProvider value={formik}>
        <form onSubmit={formik.handleSubmit} className="p-fluid">
          {/* Provider (optional) */}
          {/* {role === 'admin' ? (
            <div className="field">
              <label htmlFor="provider">Provider</label>
              <InputText
                id="provider"
                name="provider"
                value={formik.values.provider}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                disabled={editMode}
              />
              {formik.touched.provider && formik.errors.provider && (
                <small className="p-error">{formik.errors.provider}</small>
              )}
            </div>
          ) : (
            ''
          )} */}

          {/* Product Name */}
          <div className="field">
            <label htmlFor="name">Product Name</label>
            <InputText
              id="name"
              name="name"
              value={formik.values.name}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.name && formik.errors.name && (
              <small className="p-error">{formik.errors.name}</small>
            )}
          </div>

          {/* Description */}
          <div className="field">
            <label htmlFor="description">Description</label>
            <InputText
              id="description"
              name="description"
              value={formik.values.description}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
            {formik.touched.description && formik.errors.description && (
              <small className="p-error">{formik.errors.description}</small>
            )}
          </div>

          {/* Category */}
          <div className="field">
            <label htmlFor="category">Category</label>
            <Dropdown
              id="category"
              name="category"
              value={formik.values.category}
              options={safeCategories.map((c) => ({ label: c.name, value: c._id }))}
              onChange={(e) => {
                const newCategory = safeCategories.find(c => c._id === e.value);
                formik.setFieldValue('category', e.value);
                // Reset subcategory only if current subcategory doesn't exist in new category
                if (!newCategory?.subCategories.some(sc => sc._id === formik.values.subCategory)) {
                  formik.setFieldValue('subCategory', '');
                }
              }}
              onBlur={formik.handleBlur}
              placeholder="Select Category"
            />
            {formik.touched.category && formik.errors.category && (
              <small className="p-error">{formik.errors.category}</small>
            )}
          </div>

          {/* Subcategory */}
          <div className="field">
            <label htmlFor="subCategory">Subcategory</label>
            <Dropdown
              id="subCategory"
              name="subCategory"
              value={formik.values.subCategory}
              options={getSubCategories().map((sc) => ({ label: sc.name, value: sc._id }))}
              onChange={(e) => formik.setFieldValue('subCategory', e.value)}
              onBlur={formik.handleBlur}
              disabled={!formik.values.category}
              placeholder="Select Subcategory"
            />
            {formik.touched.subCategory && formik.errors.subCategory && (
              <small className="p-error">{formik.errors.subCategory}</small>
            )}
          </div>

          {/* Delivery */}
          <div className="field">
            <label htmlFor="delivery">Delivery</label>
            <Dropdown
              id="delivery"
              name="delivery"
              value={formik.values.delivery}
              options={yesNoOptions}
              onChange={(e) => formik.setFieldValue('delivery', e.value)}
              onBlur={formik.handleBlur}
              placeholder="Select delivery option"
            />
            {formik.touched.delivery && formik.errors.delivery && (
              <small className="p-error">{formik.errors.delivery}</small>
            )}
          </div>

          {/* Minimum Delivery Amount */}
          <div className="field">
            <label htmlFor="minDeliveryAmount">Minimum Delivery Amount</label>
            <InputNumber
              id="minDeliveryAmount"
              name="minDeliveryAmount"
              value={formik.values.minDeliveryAmount}
              onValueChange={(e) => formik.setFieldValue('minDeliveryAmount', e.value)}
              onBlur={formik.handleBlur}
              mode="decimal"
            />
            {formik.touched.minDeliveryAmount && formik.errors.minDeliveryAmount && (
              <small className="p-error">{formik.errors.minDeliveryAmount}</small>
            )}
          </div>
          {/* Shipping Charges */}
          <div className="field">
            <label htmlFor="shippingCharges">Shipping Charges</label>
            <InputNumber
              id="shippingCharges"
              name="shippingCharges"
              value={formik.values.shippingCharges}
              onValueChange={(e) => formik.setFieldValue('shippingCharges', e.value)}
              onBlur={formik.handleBlur}
              mode="decimal"
            />
            {formik.touched.shippingCharges && formik.errors.shippingCharges && (
              <small className="p-error">{formik.errors.shippingCharges}</small>
            )}
          </div>

          {/* Photos (Image File Upload) */}
          <div className="field">
            <label htmlFor="photo" className='block text-sm font-medium'>Photos</label>
            <input
              id="photo"
              name="photo"
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/webp,image/svg+xml"
              multiple
              onChange={handleFileChange}
              className="w-full mt-2 p-2 border rounded-md"
            />
            {formik.touched.photo && formik.errors.photo && (
              <small className="p-error">{formik.errors.photo as string}</small>
            )}
            {/* If editing, show existing photo previews with an eye button */}
            {editMode &&
              initialValues.photo &&
              Array.isArray(initialValues.photo) &&
              initialValues.photo.length > 0 && (
                <div className="p-mt-2">
                  <label>Existing Photos:</label>
                  <div className="p-d-flex p-flex-wrap gap-2">
                    {initialValues.photo.map((photoUrl, index) => (
                      <div key={index} className="p-d-flex align-items-center gap-1">
                        <img
                          src={photoUrl as string}
                          alt={`Photo ${index}`}
                          style={{ width: '50px', height: '50px', objectFit: 'cover' }}
                        />
                        <Button
                          icon="pi pi-eye"
                          className="p-button-text"
                          onClick={() => window.open(photoUrl as string, '_blank')}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>

          {/* Variants */}
          <div className="field">
            <label>Variants</label>
            <FieldArray
              name="variants"
              render={(arrayHelpers) => (
                <div>
                  {formik.values.variants && formik.values.variants.length > 0 ? (
                    formik.values.variants.map((variant, index) => (
                      <div key={index} className="p-mb-4 p-shadow-2 p-p-2">
                        <h5>Variant {index + 1}</h5>
                        <div className="grid">
                          <div className="col-3">
                            <label htmlFor={`variants[${index}].amount`}>Amount</label>
                            <InputNumber
                              id={`variants[${index}].amount`}
                              name={`variants[${index}].amount`}
                              value={variant.amount}
                              onValueChange={(e) =>
                                formik.setFieldValue(`variants[${index}].amount`, e.value)
                              }
                              onBlur={formik.handleBlur}
                            />
                            {formik.touched.variants &&
                              formik.touched.variants[index] &&
                              formik.errors.variants &&
                              (formik.errors.variants as any)[index]?.amount && (
                                <small className="p-error">
                                  {(formik.errors.variants as any)[index].amount}
                                </small>
                              )}
                          </div>
                          <div className="col-3">
                            <label htmlFor={`variants[${index}].unit`}>Unit</label>
                            <Dropdown
                              id={`variants[${index}].unit`}
                              name={`variants[${index}].unit`}
                              value={variant.unit}
                              options={units}
                              onChange={(e) =>
                                formik.setFieldValue(`variants[${index}].unit`, e.value)
                              }
                              onBlur={formik.handleBlur}
                              placeholder="Select unit"
                            />
                            {formik.touched.variants &&
                              formik.touched.variants[index] &&
                              formik.errors.variants &&
                              (formik.errors.variants as any)[index]?.unit && (
                                <small className="p-error">
                                  {(formik.errors.variants as any)[index].unit}
                                </small>
                              )}
                          </div>
                          <div className="col-3">
                            <label htmlFor={`variants[${index}].price`}>Price</label>
                            <InputNumber
                              id={`variants[${index}].price`}
                              name={`variants[${index}].price`}
                              value={variant.price}
                              onValueChange={(e) =>
                                formik.setFieldValue(`variants[${index}].price`, e.value)
                              }
                              onBlur={formik.handleBlur}
                              mode="decimal"
                            />
                            {formik.touched.variants &&
                              formik.touched.variants[index] &&
                              formik.errors.variants &&
                              (formik.errors.variants as any)[index]?.price && (
                                <small className="p-error">
                                  {(formik.errors.variants as any)[index].price}
                                </small>
                              )}
                          </div>
                          <div className="col-3">
                            <label htmlFor={`variants[${index}].fake_price`}>Fake Price</label>
                            <InputNumber
                              id={`variants[${index}].fake_price`}
                              name={`variants[${index}].fake_price`}
                              value={variant.fake_price}
                              onValueChange={(e) =>
                                formik.setFieldValue(`variants[${index}].fake_price`, e.value)
                              }
                              onBlur={formik.handleBlur}
                              mode="decimal"
                            />
                            {formik.touched.variants &&
                              formik.touched.variants[index] &&
                              formik.errors.variants &&
                              (formik.errors.variants as any)[index]?.fake_price && (
                                <small className="p-error">
                                  {(formik.errors.variants as any)[index].fake_price}
                                </small>
                              )}
                          </div>
                        </div>
                        <div className="grid mt-2">
                          <div className="col-4">
                            <label htmlFor={`variants[${index}].stock`}>Stock</label>
                            <InputNumber
                              id={`variants[${index}].stock`}
                              name={`variants[${index}].stock`}
                              value={variant.stock}
                              onValueChange={(e) =>
                                formik.setFieldValue(`variants[${index}].stock`, e.value)
                              }
                              onBlur={formik.handleBlur}
                              mode="decimal"
                            />
                            {formik.touched.variants &&
                              formik.touched.variants[index] &&
                              formik.errors.variants &&
                              (formik.errors.variants as any)[index]?.stock && (
                                <small className="p-error">
                                  {(formik.errors.variants as any)[index].stock}
                                </small>
                              )}
                          </div>
                          <div className="col-4">
                            <label htmlFor={`variants[${index}].default`}>Default</label>
                            <Dropdown
                              id={`variants[${index}].default`}
                              name={`variants[${index}].default`}
                              value={variant.default}
                              options={yesNoOptions}
                              onChange={(e) =>
                                formik.setFieldValue(`variants[${index}].default`, e.value)
                              }
                              onBlur={formik.handleBlur}
                              placeholder="Default?"
                            />
                            {formik.touched.variants &&
                              formik.touched.variants[index] &&
                              formik.errors.variants &&
                              (formik.errors.variants as any)[index]?.default && (
                                <small className="p-error">
                                  {(formik.errors.variants as any)[index].default}
                                </small>
                              )}
                          </div>
                          <div className="col-4">
                            <label htmlFor={`variants[${index}].flavour`}>Flavour (optional)</label>
                            <InputText
                              id={`variants[${index}].flavour`}
                              name={`variants[${index}].flavour`}
                              value={variant.flavour}
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </div>
                        </div>
                        <div className="mt-2">
                          <Button
                            type="button"
                            icon="pi pi-minus-circle"
                            className="p-button-danger p-button-text"
                            label="Remove Variant"
                            onClick={() => arrayHelpers.remove(index)}
                          />
                        </div>
                      </div>
                    ))
                  ) : (
                    <div>No variants added.</div>
                  )}
                  <Button
                    type="button"
                    label="Add Variant"
                    icon="pi pi-plus-circle"
                    onClick={() =>
                      arrayHelpers.push({
                        amount: 0,
                        unit: 'Piece',
                        default: false,
                        fake_price: 0,
                        price: 0,
                        flavour: '',
                        stock: 0,
                      })
                    }
                  />
                </div>
              )}
            />
          </div>

          {/* Product Usages */}
          <div className="field">
            <label>Product Usages</label>
            {safeQuestions.map((question) => {
              // Find the corresponding usage for this question
              const existingUsageIndex = formik.values.productUsages.findIndex(
                (u) => u.question === question._id
              );
              const usage =
                existingUsageIndex > -1
                  ? formik.values.productUsages[existingUsageIndex]
                  : { question: question._id, option: '' };

              return (
                <div key={question._id} className="p-mb-3 p-shadow-2 p-p-2">
                  <input type="hidden" name="" value={question._id} />
                  <h5>{question.value}</h5>
                  <div className="field">
                    <Dropdown
                      id={`productUsages_${question._id}`}
                      name={`productUsages_${question._id}`}
                      value={usage.option}
                      options={question.options.map((opt) => ({
                        label: opt.value,
                        value: opt._id,
                      }))}
                      onChange={(e) => {
                        if (existingUsageIndex > -1) {
                          const updatedUsages = [...formik.values.productUsages];
                          updatedUsages[existingUsageIndex] = {
                            ...updatedUsages[existingUsageIndex],
                            option: e.value,
                          };
                          formik.setFieldValue('productUsages', updatedUsages);
                        } else {
                          formik.setFieldValue('productUsages', [
                            ...formik.values.productUsages,
                            { question: question._id, option: e.value },
                          ]);
                        }
                      }}
                      onBlur={formik.handleBlur}
                      placeholder="Select Option"
                    />
                    {formik.touched.productUsages &&
                      Array.isArray(formik.errors.productUsages) &&
                      (formik.errors.productUsages as any).find(
                        (err: any) => err && err.question === question._id
                      ) && (
                        <small className="p-error">
                          {
                            (formik.errors.productUsages as any).find(
                              (err: any) => err && err.question === question._id
                            )?.option
                          }
                        </small>
                      )}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="field">
            <Button
              type="submit"
              label={editMode ? 'Update Product' : 'Create Product'}
              className="p-mt-2"
              disabled={isLoading}
              loading={isLoading}
            />

          </div>
        </form>
      </FormikProvider>
    </Dialog>
  );
};

export default ProductForm;
